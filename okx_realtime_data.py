#!/usr/bin/env python3
"""
OKX交易所实时数据监控工具
实时获取和显示合约排行榜、价格走势和动态更新
"""

import requests
import json
import time
import threading
from datetime import datetime
import os
import sys

class OKXRealTimeMonitor:
    def __init__(self):
        self.base_url = "https://www.okx.com/api/v5"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
        }
        self.running = True
        self.last_data = {}
        
    def clear_screen(self):
        """清屏"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def get_futures_data(self):
        """获取永续合约实时数据"""
        try:
            response = requests.get(f"{self.base_url}/market/tickers?instType=SWAP", 
                                  headers=self.headers, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            if data.get('code') == '0':
                return data.get('data', [])
            else:
                print(f"❌ API错误: {data.get('msg')}")
                return []
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return []
    
    def process_ticker_data(self, tickers):
        """处理ticker数据"""
        processed = []
        for ticker in tickers:
            try:
                inst_id = ticker.get('instId', '')
                last_price = float(ticker.get('last', 0))
                open_24h = float(ticker.get('open24h', 0))
                
                if open_24h > 0:
                    change_24h = ((last_price - open_24h) / open_24h * 100)
                else:
                    change_24h = 0
                
                # 计算价格变化趋势
                trend = "📈"
                if inst_id in self.last_data:
                    last_price_prev = self.last_data[inst_id]['price']
                    if last_price > last_price_prev:
                        trend = "🔥"
                    elif last_price < last_price_prev:
                        trend = "❄️"
                    else:
                        trend = "➡️"
                
                item = {
                    'symbol': inst_id,
                    'price': last_price,
                    'change_24h': round(change_24h, 2),
                    'volume_24h': float(ticker.get('volCcy24h', 0)),
                    'high_24h': float(ticker.get('high24h', 0)),
                    'low_24h': float(ticker.get('low24h', 0)),
                    'bid': float(ticker.get('bidPx', 0)),
                    'ask': float(ticker.get('askPx', 0)),
                    'trend': trend,
                    'timestamp': int(ticker.get('ts', 0))
                }
                processed.append(item)
                
                # 更新历史数据
                self.last_data[inst_id] = {'price': last_price}
                
            except Exception as e:
                continue
        
        return processed
    
    def display_rankings(self, data):
        """显示排行榜"""
        self.clear_screen()
        
        # 按成交额排序
        volume_ranking = sorted(data, key=lambda x: x['volume_24h'], reverse=True)[:20]
        
        # 按涨幅排序
        gainers = sorted([x for x in data if x['change_24h'] > 0], 
                        key=lambda x: x['change_24h'], reverse=True)[:15]
        
        # 按跌幅排序  
        losers = sorted([x for x in data if x['change_24h'] < 0], 
                       key=lambda x: x['change_24h'])[:15]
        
        print("🚀 OKX永续合约实时数据监控")
        print(f"⏰ 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📊 监控币种: {len(data)} 个")
        
        # 成交额排行榜
        print(f"\n{'='*100}")
        print("💰 24小时成交额排行榜 (TOP 20)")
        print(f"{'='*100}")
        print(f"{'排名':<4} {'币种':<18} {'价格':<15} {'24h涨跌':<12} {'成交额(USDT)':<18} {'趋势':<4}")
        print(f"{'-'*100}")
        
        for i, item in enumerate(volume_ranking, 1):
            change_color = "🔥" if item['change_24h'] > 5 else "📈" if item['change_24h'] > 0 else "📉"
            print(f"{i:<4} {item['symbol']:<18} {item['price']:<15.6f} "
                  f"{change_color}{item['change_24h']:>7.2f}% {item['volume_24h']:>16,.0f} {item['trend']:<4}")
        
        # 涨幅榜
        print(f"\n{'='*80}")
        print("🚀 24小时涨幅榜 (TOP 15)")
        print(f"{'='*80}")
        print(f"{'排名':<4} {'币种':<18} {'价格':<15} {'涨幅':<10} {'趋势':<4}")
        print(f"{'-'*80}")
        
        for i, item in enumerate(gainers, 1):
            print(f"{i:<4} {item['symbol']:<18} {item['price']:<15.6f} "
                  f"🔥{item['change_24h']:>6.2f}% {item['trend']:<4}")
        
        # 跌幅榜
        print(f"\n{'='*80}")
        print("📉 24小时跌幅榜 (TOP 15)")
        print(f"{'='*80}")
        print(f"{'排名':<4} {'币种':<18} {'价格':<15} {'跌幅':<10} {'趋势':<4}")
        print(f"{'-'*80}")
        
        for i, item in enumerate(losers, 1):
            print(f"{i:<4} {item['symbol']:<18} {item['price']:<15.6f} "
                  f"❄️{item['change_24h']:>6.2f}% {item['trend']:<4}")
        
        print(f"\n{'='*60}")
        print("💡 实时监控中... 按 Ctrl+C 退出")
        print("🔄 数据每10秒自动更新")
        print(f"{'='*60}")
    
    def get_hot_coins_detail(self, data, count=5):
        """获取热门币种详细信息"""
        hot_coins = sorted(data, key=lambda x: x['volume_24h'], reverse=True)[:count]
        
        print(f"\n{'='*80}")
        print(f"🔥 热门币种实时详情 (TOP {count})")
        print(f"{'='*80}")
        
        for coin in hot_coins:
            print(f"\n📊 {coin['symbol']}")
            print(f"   💰 当前价格: {coin['price']:.6f}")
            print(f"   📈 24h涨跌: {coin['change_24h']:+.2f}%")
            print(f"   📊 买一价: {coin['bid']:.6f} | 卖一价: {coin['ask']:.6f}")
            print(f"   📈 24h最高: {coin['high_24h']:.6f} | 最低: {coin['low_24h']:.6f}")
            print(f"   💵 24h成交额: {coin['volume_24h']:,.0f} USDT")
            print(f"   🔄 实时趋势: {coin['trend']}")
    
    def monitor_specific_coins(self, symbols):
        """监控特定币种"""
        print(f"\n{'='*80}")
        print(f"👀 监控特定币种: {', '.join(symbols)}")
        print(f"{'='*80}")
        
        for symbol in symbols:
            try:
                response = requests.get(f"{self.base_url}/market/ticker?instId={symbol}", 
                                      headers=self.headers, timeout=5)
                data = response.json()
                
                if data.get('code') == '0' and data.get('data'):
                    ticker = data['data'][0]
                    last_price = float(ticker.get('last', 0))
                    open_24h = float(ticker.get('open24h', 0))
                    change_24h = ((last_price - open_24h) / open_24h * 100) if open_24h > 0 else 0
                    
                    trend_icon = "🔥" if change_24h > 0 else "❄️" if change_24h < 0 else "➡️"
                    
                    print(f"📊 {symbol}: {last_price:.6f} ({trend_icon}{change_24h:+.2f}%) "
                          f"成交额: {float(ticker.get('volCcy24h', 0)):,.0f}")
                else:
                    print(f"❌ {symbol}: 获取数据失败")
            except:
                print(f"❌ {symbol}: 网络错误")
    
    def start_monitoring(self, update_interval=10):
        """开始实时监控"""
        print("🚀 启动OKX实时数据监控...")
        
        try:
            while self.running:
                # 获取数据
                tickers = self.get_futures_data()
                if tickers:
                    processed_data = self.process_ticker_data(tickers)
                    self.display_rankings(processed_data)
                    
                    # 显示热门币种详情
                    self.get_hot_coins_detail(processed_data, 5)
                    
                    # 监控特定热门币种
                    hot_symbols = [item['symbol'] for item in 
                                 sorted(processed_data, key=lambda x: x['volume_24h'], reverse=True)[:3]]
                    self.monitor_specific_coins(hot_symbols)
                
                # 等待下次更新
                for i in range(update_interval):
                    if not self.running:
                        break
                    time.sleep(1)
                    
        except KeyboardInterrupt:
            print("\n\n👋 监控已停止")
            self.running = False
        except Exception as e:
            print(f"\n❌ 监控出错: {e}")

def main():
    """主函数"""
    monitor = OKXRealTimeMonitor()
    
    print("🚀 OKX交易所实时数据监控工具")
    print("=" * 50)
    print("1. 实时获取所有永续合约数据")
    print("2. 动态显示排行榜和价格变化")
    print("3. 监控热门币种详细信息")
    print("4. 每10秒自动更新数据")
    print("=" * 50)
    
    input("按回车键开始监控...")
    
    # 开始实时监控
    monitor.start_monitoring(update_interval=10)

if __name__ == "__main__":
    main()
