#!/usr/bin/env python3
"""
OKX交易所WebSocket实时数据监控
使用WebSocket获取真正的实时数据流
"""

import json
import time
import threading
from datetime import datetime
import requests
import websocket
import os

class OKXWebSocketMonitor:
    def __init__(self):
        self.ws_url = "wss://ws.okx.com:8443/ws/v5/public"
        self.rest_url = "https://www.okx.com/api/v5"
        self.ws = None
        self.running = True
        self.price_data = {}
        self.volume_data = {}
        self.top_symbols = []
        
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
    
    def clear_screen(self):
        """清屏"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def get_top_contracts(self, limit=50):
        """获取成交量最大的合约列表"""
        try:
            response = requests.get(f"{self.rest_url}/market/tickers?instType=SWAP", 
                                  headers=self.headers, timeout=10)
            data = response.json()
            
            if data.get('code') == '0':
                tickers = data.get('data', [])
                # 按成交额排序
                sorted_tickers = sorted(tickers, 
                                      key=lambda x: float(x.get('volCcy24h', 0)), 
                                      reverse=True)
                
                # 获取前N个交易对
                top_contracts = [ticker['instId'] for ticker in sorted_tickers[:limit]]
                
                # 初始化数据
                for ticker in sorted_tickers[:limit]:
                    symbol = ticker['instId']
                    self.price_data[symbol] = {
                        'last': float(ticker.get('last', 0)),
                        'open24h': float(ticker.get('open24h', 0)),
                        'high24h': float(ticker.get('high24h', 0)),
                        'low24h': float(ticker.get('low24h', 0)),
                        'change24h': 0,
                        'timestamp': int(ticker.get('ts', 0))
                    }
                    self.volume_data[symbol] = {
                        'vol24h': float(ticker.get('vol24h', 0)),
                        'volCcy24h': float(ticker.get('volCcy24h', 0))
                    }
                
                return top_contracts
            return []
        except Exception as e:
            print(f"❌ 获取合约列表失败: {e}")
            return []
    
    def on_message(self, ws, message):
        """处理WebSocket消息"""
        try:
            data = json.loads(message)
            
            if 'data' in data:
                for item in data['data']:
                    symbol = item.get('instId')
                    if symbol and symbol in self.price_data:
                        # 更新价格数据
                        last_price = float(item.get('last', 0))
                        open_24h = self.price_data[symbol]['open24h']
                        
                        if open_24h > 0:
                            change_24h = ((last_price - open_24h) / open_24h * 100)
                        else:
                            change_24h = 0
                        
                        self.price_data[symbol].update({
                            'last': last_price,
                            'high24h': float(item.get('high24h', self.price_data[symbol]['high24h'])),
                            'low24h': float(item.get('low24h', self.price_data[symbol]['low24h'])),
                            'change24h': change_24h,
                            'timestamp': int(item.get('ts', 0))
                        })
                        
                        # 更新成交量数据
                        if 'vol24h' in item:
                            self.volume_data[symbol]['vol24h'] = float(item.get('vol24h', 0))
                        if 'volCcy24h' in item:
                            self.volume_data[symbol]['volCcy24h'] = float(item.get('volCcy24h', 0))
                            
        except Exception as e:
            print(f"❌ 处理消息失败: {e}")
    
    def on_error(self, ws, error):
        """处理WebSocket错误"""
        print(f"❌ WebSocket错误: {error}")
    
    def on_close(self, ws, close_status_code, close_msg):
        """WebSocket连接关闭"""
        print("🔌 WebSocket连接已关闭")
    
    def on_open(self, ws):
        """WebSocket连接建立"""
        print("✅ WebSocket连接已建立")
        
        # 订阅ticker数据
        subscribe_msg = {
            "op": "subscribe",
            "args": []
        }
        
        # 为每个交易对添加订阅
        for symbol in self.top_symbols:
            subscribe_msg["args"].append({
                "channel": "tickers",
                "instId": symbol
            })
        
        ws.send(json.dumps(subscribe_msg))
        print(f"📡 已订阅 {len(self.top_symbols)} 个交易对的实时数据")
    
    def display_realtime_data(self):
        """显示实时数据"""
        while self.running:
            try:
                self.clear_screen()
                
                # 准备显示数据
                display_data = []
                for symbol in self.top_symbols:
                    if symbol in self.price_data and symbol in self.volume_data:
                        price_info = self.price_data[symbol]
                        volume_info = self.volume_data[symbol]
                        
                        display_data.append({
                            'symbol': symbol,
                            'price': price_info['last'],
                            'change24h': price_info['change24h'],
                            'high24h': price_info['high24h'],
                            'low24h': price_info['low24h'],
                            'volume': volume_info['volCcy24h'],
                            'timestamp': price_info['timestamp']
                        })
                
                # 按成交额排序
                volume_ranking = sorted(display_data, key=lambda x: x['volume'], reverse=True)[:20]
                
                # 按涨幅排序
                gainers = sorted([x for x in display_data if x['change24h'] > 0], 
                               key=lambda x: x['change24h'], reverse=True)[:10]
                
                # 按跌幅排序
                losers = sorted([x for x in display_data if x['change24h'] < 0], 
                              key=lambda x: x['change24h'])[:10]
                
                print("🚀 OKX永续合约实时数据流监控")
                print(f"⏰ 实时更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"📡 WebSocket连接状态: {'🟢 已连接' if self.ws and self.ws.sock and self.ws.sock.connected else '🔴 未连接'}")
                print(f"📊 监控币种: {len(display_data)} 个")
                
                # 成交额排行榜
                print(f"\n{'='*90}")
                print("💰 实时成交额排行榜 (TOP 20)")
                print(f"{'='*90}")
                print(f"{'排名':<4} {'币种':<18} {'实时价格':<15} {'24h涨跌':<12} {'成交额(USDT)':<18}")
                print(f"{'-'*90}")
                
                for i, item in enumerate(volume_ranking, 1):
                    change_icon = "🔥" if item['change24h'] > 5 else "📈" if item['change24h'] > 0 else "📉" if item['change24h'] < -5 else "➡️"
                    print(f"{i:<4} {item['symbol']:<18} {item['price']:<15.6f} "
                          f"{change_icon}{item['change24h']:>7.2f}% {item['volume']:>16,.0f}")
                
                # 涨幅榜
                print(f"\n{'='*70}")
                print("🚀 实时涨幅榜 (TOP 10)")
                print(f"{'='*70}")
                print(f"{'排名':<4} {'币种':<18} {'实时价格':<15} {'涨幅':<10}")
                print(f"{'-'*70}")
                
                for i, item in enumerate(gainers, 1):
                    print(f"{i:<4} {item['symbol']:<18} {item['price']:<15.6f} 🔥{item['change24h']:>6.2f}%")
                
                # 跌幅榜
                print(f"\n{'='*70}")
                print("📉 实时跌幅榜 (TOP 10)")
                print(f"{'='*70}")
                print(f"{'排名':<4} {'币种':<18} {'实时价格':<15} {'跌幅':<10}")
                print(f"{'-'*70}")
                
                for i, item in enumerate(losers, 1):
                    print(f"{i:<4} {item['symbol']:<18} {item['price']:<15.6f} ❄️{item['change24h']:>6.2f}%")
                
                print(f"\n{'='*60}")
                print("💡 实时WebSocket数据流监控中...")
                print("🔄 数据实时更新，按 Ctrl+C 退出")
                print(f"{'='*60}")
                
                time.sleep(2)  # 每2秒刷新显示
                
            except Exception as e:
                print(f"❌ 显示数据出错: {e}")
                time.sleep(5)
    
    def start_monitoring(self):
        """开始监控"""
        print("🚀 启动OKX WebSocket实时监控...")
        
        # 获取热门合约
        print("📊 获取热门合约列表...")
        self.top_symbols = self.get_top_contracts(50)
        
        if not self.top_symbols:
            print("❌ 无法获取合约列表")
            return
        
        print(f"✅ 获取到 {len(self.top_symbols)} 个热门合约")
        
        # 启动显示线程
        display_thread = threading.Thread(target=self.display_realtime_data)
        display_thread.daemon = True
        display_thread.start()
        
        # 建立WebSocket连接
        try:
            websocket.enableTrace(False)
            self.ws = websocket.WebSocketApp(
                self.ws_url,
                on_message=self.on_message,
                on_error=self.on_error,
                on_close=self.on_close,
                on_open=self.on_open
            )
            
            self.ws.run_forever()
            
        except KeyboardInterrupt:
            print("\n\n👋 监控已停止")
            self.running = False
        except Exception as e:
            print(f"\n❌ WebSocket连接失败: {e}")

def main():
    """主函数"""
    print("🚀 OKX交易所WebSocket实时数据监控")
    print("=" * 60)
    print("✨ 功能特点:")
    print("  • 真正的实时数据流 (WebSocket)")
    print("  • 实时价格变化监控")
    print("  • 动态排行榜更新")
    print("  • 热门合约自动筛选")
    print("=" * 60)
    
    # 检查依赖
    try:
        import websocket
    except ImportError:
        print("❌ 缺少websocket-client库")
        print("请运行: pip install websocket-client")
        return
    
    input("按回车键开始实时监控...")
    
    monitor = OKXWebSocketMonitor()
    monitor.start_monitoring()

if __name__ == "__main__":
    main()
