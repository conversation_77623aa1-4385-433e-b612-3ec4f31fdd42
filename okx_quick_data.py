#!/usr/bin/env python3
"""
OKX交易所实时数据获取工具
实时获取合约排行榜和价格走势，支持动态更新
"""

import requests
import json
import time
import threading
from datetime import datetime
import os
import sys

def get_okx_futures_ranking():
    """
    获取OKX永续合约排行榜数据
    """
    base_url = "https://www.okx.com/api/v5"
    
    # 设置请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json'
    }
    
    try:
        # 获取所有永续合约的ticker数据
        print("🔄 正在获取OKX永续合约数据...")
        response = requests.get(f"{base_url}/market/tickers?instType=SWAP", headers=headers)
        response.raise_for_status()
        
        data = response.json()
        if data.get('code') != '0':
            print(f"❌ API错误: {data.get('msg')}")
            return
        
        tickers = data.get('data', [])
        print(f"✅ 获取到 {len(tickers)} 个永续合约数据")
        
        # 处理数据
        processed_data = []
        for ticker in tickers:
            try:
                last_price = float(ticker.get('last', 0))
                open_24h = float(ticker.get('open24h', 0))
                change_24h = ((last_price - open_24h) / open_24h * 100) if open_24h > 0 else 0
                
                item = {
                    'symbol': ticker.get('instId', ''),
                    'price': last_price,
                    'change_24h': round(change_24h, 2),
                    'volume_24h': float(ticker.get('volCcy24h', 0)),
                    'high_24h': float(ticker.get('high24h', 0)),
                    'low_24h': float(ticker.get('low24h', 0))
                }
                processed_data.append(item)
            except:
                continue
        
        # 按成交额排序
        volume_ranking = sorted(processed_data, key=lambda x: x['volume_24h'], reverse=True)
        
        # 按涨幅排序
        gainers = sorted([x for x in processed_data if x['change_24h'] > 0], 
                        key=lambda x: x['change_24h'], reverse=True)
        
        # 按跌幅排序
        losers = sorted([x for x in processed_data if x['change_24h'] < 0], 
                       key=lambda x: x['change_24h'])
        
        # 显示结果
        print_ranking(volume_ranking[:20], "📊 成交额排行榜 (TOP 20)")
        print_ranking(gainers[:15], "🚀 涨幅榜 (TOP 15)")
        print_ranking(losers[:15], "📉 跌幅榜 (TOP 15)")
        
        return {
            'volume_ranking': volume_ranking,
            'gainers': gainers,
            'losers': losers,
            'total_count': len(processed_data)
        }
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
    except Exception as e:
        print(f"❌ 处理数据失败: {e}")

def print_ranking(data, title):
    """
    打印排行榜
    """
    print(f"\n{'='*90}")
    print(f"{title}")
    print(f"{'='*90}")
    print(f"{'排名':<4} {'交易对':<20} {'价格':<15} {'24h涨跌':<12} {'24h成交额(USDT)':<20}")
    print(f"{'-'*90}")
    
    for i, item in enumerate(data, 1):
        change_icon = "🔥" if item['change_24h'] > 5 else "📈" if item['change_24h'] > 0 else "📉" if item['change_24h'] < -5 else "📊"
        
        print(f"{i:<4} {item['symbol']:<20} {item['price']:<15.6f} "
              f"{change_icon}{item['change_24h']:>7.2f}% {item['volume_24h']:>18,.0f}")

def get_specific_coin_data(symbol):
    """
    获取特定币种的详细数据
    """
    base_url = "https://www.okx.com/api/v5"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json'
    }
    
    try:
        # 获取ticker数据
        response = requests.get(f"{base_url}/market/ticker?instId={symbol}", headers=headers)
        response.raise_for_status()
        
        data = response.json()
        if data.get('code') != '0':
            print(f"❌ 获取{symbol}数据失败: {data.get('msg')}")
            return
        
        ticker = data.get('data', [{}])[0]
        
        # 获取K线数据
        candle_response = requests.get(f"{base_url}/market/candles?instId={symbol}&bar=1D&limit=7", headers=headers)
        candle_data = candle_response.json().get('data', [])
        
        print(f"\n{'='*60}")
        print(f"📊 {symbol} 详细信息")
        print(f"{'='*60}")
        print(f"最新价格: {float(ticker.get('last', 0)):.6f}")
        print(f"24h涨跌: {((float(ticker.get('last', 0)) - float(ticker.get('open24h', 0))) / float(ticker.get('open24h', 1)) * 100):.2f}%")
        print(f"24h最高: {float(ticker.get('high24h', 0)):.6f}")
        print(f"24h最低: {float(ticker.get('low24h', 0)):.6f}")
        print(f"24h成交量: {float(ticker.get('vol24h', 0)):,.0f}")
        print(f"24h成交额: {float(ticker.get('volCcy24h', 0)):,.0f} USDT")
        
        if candle_data:
            print(f"\n📈 近7日价格走势:")
            print(f"{'日期':<12} {'开盘':<12} {'最高':<12} {'最低':<12} {'收盘':<12}")
            print(f"{'-'*60}")
            for candle in candle_data[:7]:
                date = datetime.fromtimestamp(int(candle[0])/1000).strftime('%m-%d')
                print(f"{date:<12} {float(candle[1]):<12.6f} {float(candle[2]):<12.6f} "
                      f"{float(candle[3]):<12.6f} {float(candle[4]):<12.6f}")
        
    except Exception as e:
        print(f"❌ 获取{symbol}详细数据失败: {e}")

def main():
    """
    主函数
    """
    print("🚀 OKX交易所合约数据获取工具")
    print(f"⏰ 获取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 获取排行榜数据
    result = get_okx_futures_ranking()
    
    if result:
        print(f"\n✅ 数据获取完成！共处理 {result['total_count']} 个合约")
        
        # 询问是否查看特定币种详情
        print(f"\n{'='*60}")
        print("💡 想查看特定币种的详细信息吗？")
        print("热门币种示例: BTC-USDT-SWAP, ETH-USDT-SWAP, SOL-USDT-SWAP")
        
        while True:
            symbol = input("\n请输入币种代码 (直接回车退出): ").strip().upper()
            if not symbol:
                break
            
            if not symbol.endswith('-SWAP'):
                symbol += '-USDT-SWAP'
            
            get_specific_coin_data(symbol)

if __name__ == "__main__":
    main()
