#!/usr/bin/env python3
"""
OKX交易所完整数据监控工具
集成所有功能：实时数据、排行榜、价格走势、WebSocket监控
"""

import requests
import json
import time
import threading
from datetime import datetime, timedelta
import pandas as pd
import os
import sys

class OKXCompleteMonitor:
    def __init__(self):
        self.base_url = "https://www.okx.com/api/v5"
        self.ws_url = "wss://ws.okx.com:8443/ws/v5/public"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        self.running = True
        self.price_data = {}
        self.last_data = {}
        
    def clear_screen(self):
        """清屏"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def get_all_instruments(self, inst_type="SWAP"):
        """获取所有合约交易对信息"""
        url = f"{self.base_url}/public/instruments"
        params = {"instType": inst_type}
        
        try:
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            if data.get('code') == '0':
                return data.get('data', [])
            else:
                print(f"❌ 获取交易对失败: {data.get('msg')}")
                return []
        except Exception as e:
            print(f"❌ 请求交易对数据失败: {e}")
            return []
    
    def get_all_tickers(self, inst_type="SWAP"):
        """获取所有合约的ticker数据"""
        url = f"{self.base_url}/market/tickers"
        params = {"instType": inst_type}
        
        try:
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            if data.get('code') == '0':
                return data.get('data', [])
            else:
                print(f"❌ 获取ticker数据失败: {data.get('msg')}")
                return []
        except Exception as e:
            print(f"❌ 请求ticker数据失败: {e}")
            return []
    
    def get_candlestick_data(self, inst_id, bar="1D", limit=30):
        """获取K线数据"""
        url = f"{self.base_url}/market/candles"
        params = {
            "instId": inst_id,
            "bar": bar,
            "limit": str(limit)
        }
        
        try:
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            if data.get('code') == '0':
                return data.get('data', [])
            else:
                print(f"❌ 获取K线数据失败: {data.get('msg')}")
                return []
        except Exception as e:
            print(f"❌ 请求K线数据失败: {e}")
            return []
    
    def process_ticker_data(self, tickers):
        """处理ticker数据"""
        processed = []
        for ticker in tickers:
            try:
                inst_id = ticker.get('instId', '')
                last_price = float(ticker.get('last', 0))
                open_24h = float(ticker.get('open24h', 0))
                
                if open_24h > 0:
                    change_24h = ((last_price - open_24h) / open_24h * 100)
                else:
                    change_24h = 0
                
                # 计算价格变化趋势
                trend = "📈"
                if inst_id in self.last_data:
                    last_price_prev = self.last_data[inst_id]['price']
                    if last_price > last_price_prev:
                        trend = "🔥"
                    elif last_price < last_price_prev:
                        trend = "❄️"
                    else:
                        trend = "➡️"
                
                item = {
                    'symbol': inst_id,
                    'price': last_price,
                    'open24h': open_24h,
                    'high24h': float(ticker.get('high24h', 0)),
                    'low24h': float(ticker.get('low24h', 0)),
                    'change_24h': round(change_24h, 2),
                    'volume_24h': float(ticker.get('vol24h', 0)),
                    'volume_ccy_24h': float(ticker.get('volCcy24h', 0)),
                    'bid': float(ticker.get('bidPx', 0)),
                    'ask': float(ticker.get('askPx', 0)),
                    'trend': trend,
                    'timestamp': int(ticker.get('ts', 0))
                }
                processed.append(item)
                
                # 更新历史数据
                self.last_data[inst_id] = {'price': last_price}
                
            except Exception as e:
                continue
        
        return processed
    
    def display_main_dashboard(self, data):
        """显示主仪表板"""
        self.clear_screen()
        
        # 数据分类
        volume_ranking = sorted(data, key=lambda x: x['volume_ccy_24h'], reverse=True)[:25]
        gainers = sorted([x for x in data if x['change_24h'] > 0], 
                        key=lambda x: x['change_24h'], reverse=True)[:20]
        losers = sorted([x for x in data if x['change_24h'] < 0], 
                       key=lambda x: x['change_24h'])[:20]
        
        print("🚀 OKX交易所完整数据监控系统")
        print(f"⏰ 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📊 监控币种: {len(data)} 个永续合约")
        print(f"💰 总成交额: {sum(item['volume_ccy_24h'] for item in data):,.0f} USDT")
        
        # 成交额排行榜
        print(f"\n{'='*110}")
        print("💰 24小时成交额排行榜 (TOP 25)")
        print(f"{'='*110}")
        print(f"{'排名':<4} {'币种':<18} {'价格':<15} {'24h涨跌':<12} {'最高/最低':<20} {'成交额(USDT)':<18} {'趋势':<4}")
        print(f"{'-'*110}")
        
        for i, item in enumerate(volume_ranking, 1):
            change_color = "🔥" if item['change_24h'] > 5 else "📈" if item['change_24h'] > 0 else "📉" if item['change_24h'] < -5 else "➡️"
            high_low = f"{item['high24h']:.6f}/{item['low24h']:.6f}"
            print(f"{i:<4} {item['symbol']:<18} {item['price']:<15.6f} "
                  f"{change_color}{item['change_24h']:>7.2f}% {high_low:<20} {item['volume_ccy_24h']:>16,.0f} {item['trend']:<4}")
        
        # 涨幅榜
        print(f"\n{'='*90}")
        print("🚀 24小时涨幅榜 (TOP 20)")
        print(f"{'='*90}")
        print(f"{'排名':<4} {'币种':<18} {'价格':<15} {'涨幅':<10} {'成交额':<15} {'趋势':<4}")
        print(f"{'-'*90}")
        
        for i, item in enumerate(gainers, 1):
            print(f"{i:<4} {item['symbol']:<18} {item['price']:<15.6f} "
                  f"🔥{item['change_24h']:>6.2f}% {item['volume_ccy_24h']:>13,.0f} {item['trend']:<4}")
        
        # 跌幅榜
        print(f"\n{'='*90}")
        print("📉 24小时跌幅榜 (TOP 20)")
        print(f"{'='*90}")
        print(f"{'排名':<4} {'币种':<18} {'价格':<15} {'跌幅':<10} {'成交额':<15} {'趋势':<4}")
        print(f"{'-'*90}")
        
        for i, item in enumerate(losers, 1):
            print(f"{i:<4} {item['symbol']:<18} {item['price']:<15.6f} "
                  f"❄️{item['change_24h']:>6.2f}% {item['volume_ccy_24h']:>13,.0f} {item['trend']:<4}")
    
    def display_hot_coins_detail(self, data, count=8):
        """显示热门币种详细信息"""
        hot_coins = sorted(data, key=lambda x: x['volume_ccy_24h'], reverse=True)[:count]
        
        print(f"\n{'='*100}")
        print(f"🔥 热门币种实时详情 (TOP {count})")
        print(f"{'='*100}")
        
        for i, coin in enumerate(hot_coins, 1):
            spread = ((coin['ask'] - coin['bid']) / coin['price'] * 100) if coin['price'] > 0 else 0
            print(f"\n{i}. 📊 {coin['symbol']}")
            print(f"   💰 当前价格: {coin['price']:.6f} | 24h变化: {coin['change_24h']:+.2f}% {coin['trend']}")
            print(f"   📊 买一/卖一: {coin['bid']:.6f} / {coin['ask']:.6f} (价差: {spread:.3f}%)")
            print(f"   📈 24h区间: {coin['low24h']:.6f} ~ {coin['high24h']:.6f}")
            print(f"   💵 24h成交: {coin['volume_24h']:,.0f} 张 | {coin['volume_ccy_24h']:,.0f} USDT")
    
    def get_price_trends(self, symbols, days=7):
        """获取真实价格走势数据"""
        print(f"\n{'='*100}")
        print(f"📈 获取真实价格走势数据 (近{days}天) - 来源: OKX官方API")
        print(f"{'='*100}")

        trends_data = {}
        for symbol in symbols:
            try:
                print(f"🔄 正在获取 {symbol} 的真实K线数据...")

                # 获取日K线数据
                candles = self.get_candlestick_data(symbol, "1D", days)

                if candles and len(candles) > 0:
                    print(f"✅ {symbol} - 成功获取 {len(candles)} 天的真实数据")

                    trend_info = {
                        'symbol': symbol,
                        'data': [],
                        'data_source': 'OKX Official API',
                        'last_update': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }

                    # 验证数据真实性
                    latest_candle = candles[0]  # 最新的K线
                    latest_timestamp = int(latest_candle[0])
                    latest_time = datetime.fromtimestamp(latest_timestamp/1000)

                    print(f"📅 最新数据时间: {latest_time.strftime('%Y-%m-%d %H:%M:%S')}")
                    print(f"💰 最新收盘价: {float(latest_candle[4]):.6f}")

                    for candle in reversed(candles):  # 按时间正序
                        candle_time = datetime.fromtimestamp(int(candle[0])/1000)
                        trend_info['data'].append({
                            'timestamp': int(candle[0]),
                            'date': candle_time.strftime('%m-%d'),
                            'full_date': candle_time.strftime('%Y-%m-%d'),
                            'open': float(candle[1]),
                            'high': float(candle[2]),
                            'low': float(candle[3]),
                            'close': float(candle[4]),
                            'volume': float(candle[5]),
                            'volume_ccy': float(candle[6]) if len(candle) > 6 else 0
                        })

                    trends_data[symbol] = trend_info

                    # 显示详细走势
                    print(f"\n📊 {symbol} 真实价格走势 (近{days}日):")
                    print(f"{'日期':<12} {'开盘价':<15} {'最高价':<15} {'最低价':<15} {'收盘价':<15} {'日涨跌%':<10} {'成交量':<15}")
                    print(f"{'-'*100}")

                    for j, day_data in enumerate(trend_info['data']):
                        if j > 0:
                            prev_close = trend_info['data'][j-1]['close']
                            change = ((day_data['close'] - prev_close) / prev_close * 100)
                            change_str = f"{change:+.2f}%"
                            change_icon = "🔥" if change > 0 else "❄️" if change < 0 else "➡️"
                        else:
                            change_str = "---"
                            change_icon = "📊"

                        print(f"{day_data['full_date']:<12} {day_data['open']:<15.6f} {day_data['high']:<15.6f} "
                              f"{day_data['low']:<15.6f} {day_data['close']:<15.6f} {change_icon}{change_str:<9} {day_data['volume']:<15,.0f}")

                    # 计算技术指标
                    self.calculate_technical_indicators(trend_info['data'], symbol)

                else:
                    print(f"❌ {symbol} - 未获取到K线数据")

                time.sleep(0.2)  # 避免请求过快

            except Exception as e:
                print(f"❌ 获取 {symbol} 走势失败: {e}")

        return trends_data

    def calculate_technical_indicators(self, price_data, symbol):
        """计算技术指标"""
        if len(price_data) < 5:
            return

        try:
            closes = [day['close'] for day in price_data]

            # 计算简单移动平均线
            if len(closes) >= 5:
                ma5 = sum(closes[-5:]) / 5
                ma7 = sum(closes[-7:]) / 7 if len(closes) >= 7 else sum(closes) / len(closes)

                current_price = closes[-1]

                print(f"\n📈 {symbol} 技术指标:")
                print(f"   📊 当前价格: {current_price:.6f}")
                print(f"   📈 5日均线: {ma5:.6f} ({'上方' if current_price > ma5 else '下方'})")
                print(f"   📈 7日均线: {ma7:.6f} ({'上方' if current_price > ma7 else '下方'})")

                # 计算价格波动率
                if len(closes) >= 7:
                    recent_changes = []
                    for i in range(1, min(8, len(closes))):
                        change = abs((closes[-i] - closes[-i-1]) / closes[-i-1] * 100)
                        recent_changes.append(change)

                    avg_volatility = sum(recent_changes) / len(recent_changes)
                    print(f"   📊 7日平均波动率: {avg_volatility:.2f}%")

                # 趋势判断
                if len(closes) >= 3:
                    trend = "上升" if closes[-1] > closes[-2] > closes[-3] else "下降" if closes[-1] < closes[-2] < closes[-3] else "震荡"
                    trend_icon = "🚀" if trend == "上升" else "📉" if trend == "下降" else "🔄"
                    print(f"   🎯 短期趋势: {trend_icon} {trend}")

        except Exception as e:
            print(f"❌ 计算技术指标失败: {e}")

    def verify_data_authenticity(self, data):
        """验证数据真实性"""
        print(f"\n{'='*80}")
        print("🔍 数据真实性验证")
        print(f"{'='*80}")

        try:
            # 检查数据时效性
            current_time = datetime.now()
            latest_updates = []

            for item in data[:5]:  # 检查前5个币种
                timestamp = item['timestamp']
                data_time = datetime.fromtimestamp(timestamp/1000)
                time_diff = (current_time - data_time).total_seconds() / 60  # 分钟差
                latest_updates.append(time_diff)

                print(f"📊 {item['symbol']}: 数据时间 {data_time.strftime('%H:%M:%S')}, "
                      f"延迟 {time_diff:.1f} 分钟")

            avg_delay = sum(latest_updates) / len(latest_updates)
            print(f"\n✅ 平均数据延迟: {avg_delay:.1f} 分钟")
            print(f"📡 数据来源: OKX官方API (https://www.okx.com/api/v5)")
            print(f"🔄 数据更新频率: 实时")

            if avg_delay < 5:
                print("✅ 数据时效性: 优秀 (延迟<5分钟)")
            elif avg_delay < 15:
                print("⚠️ 数据时效性: 良好 (延迟<15分钟)")
            else:
                print("❌ 数据时效性: 需要关注 (延迟>15分钟)")

        except Exception as e:
            print(f"❌ 验证数据真实性失败: {e}")
    
    def save_data_to_files(self, data):
        """保存数据到文件"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # 保存完整数据
            df_all = pd.DataFrame(data)
            df_all.to_csv(f"okx_all_data_{timestamp}.csv", index=False, encoding='utf-8-sig')
            
            # 保存成交额排行
            volume_ranking = sorted(data, key=lambda x: x['volume_ccy_24h'], reverse=True)[:50]
            df_volume = pd.DataFrame(volume_ranking)
            df_volume.to_csv(f"okx_volume_ranking_{timestamp}.csv", index=False, encoding='utf-8-sig')
            
            # 保存涨跌幅排行
            gainers = sorted([x for x in data if x['change_24h'] > 0], 
                           key=lambda x: x['change_24h'], reverse=True)[:30]
            losers = sorted([x for x in data if x['change_24h'] < 0], 
                          key=lambda x: x['change_24h'])[:30]
            
            df_gainers = pd.DataFrame(gainers)
            df_losers = pd.DataFrame(losers)
            df_gainers.to_csv(f"okx_gainers_{timestamp}.csv", index=False, encoding='utf-8-sig')
            df_losers.to_csv(f"okx_losers_{timestamp}.csv", index=False, encoding='utf-8-sig')
            
            print(f"\n✅ 数据已保存:")
            print(f"   📄 完整数据: okx_all_data_{timestamp}.csv")
            print(f"   📄 成交额排行: okx_volume_ranking_{timestamp}.csv")
            print(f"   📄 涨幅榜: okx_gainers_{timestamp}.csv")
            print(f"   📄 跌幅榜: okx_losers_{timestamp}.csv")
            
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
    
    def start_complete_monitoring(self, update_interval=15):
        """开始完整监控"""
        print("🚀 启动OKX完整数据监控系统...")
        print("📡 连接到OKX官方API获取真实数据...")

        try:
            cycle_count = 0
            while self.running:
                cycle_count += 1

                # 获取实时数据
                print(f"🔄 正在获取最新真实数据... (周期 #{cycle_count})")
                tickers = self.get_all_tickers("SWAP")

                if tickers:
                    processed_data = self.process_ticker_data(tickers)

                    # 验证数据真实性
                    if cycle_count == 1:
                        self.verify_data_authenticity(processed_data)

                    # 显示主仪表板
                    self.display_main_dashboard(processed_data)

                    # 显示热门币种详情
                    self.display_hot_coins_detail(processed_data, 8)

                    # 每2个周期获取一次价格走势 (更频繁获取真实走势)
                    if cycle_count % 2 == 1:
                        hot_symbols = [item['symbol'] for item in
                                     sorted(processed_data, key=lambda x: x['volume_ccy_24h'], reverse=True)[:3]]
                        print(f"\n🎯 获取TOP3热门币种的真实走势数据...")
                        trends = self.get_price_trends(hot_symbols, 10)  # 获取10天数据

                        # 显示实时价格对比
                        self.show_realtime_price_comparison(hot_symbols)

                    # 每4个周期保存一次数据
                    if cycle_count % 4 == 0:
                        self.save_data_to_files(processed_data)
                        print(f"💾 数据已保存 (周期 #{cycle_count})")

                    print(f"\n{'='*100}")
                    print(f"💡 OKX真实数据监控运行中... (周期 #{cycle_count})")
                    print(f"📊 监控币种: {len(processed_data)} 个永续合约")
                    print(f"🔄 下次更新: {update_interval} 秒后")
                    print(f"� 数据来源: OKX官方API (实时)")
                    print(f"✅ 功能: 真实数据 + 排行榜 + 价格走势 + 技术指标 + 数据保存")
                    print("⌨️  按 Ctrl+C 退出监控")
                    print(f"{'='*100}")
                else:
                    print("❌ 获取数据失败，正在重试...")

                # 等待下次更新
                for i in range(update_interval):
                    if not self.running:
                        break
                    time.sleep(1)

        except KeyboardInterrupt:
            print("\n\n👋 监控已停止")
            self.running = False
        except Exception as e:
            print(f"\n❌ 监控出错: {e}")

    def show_realtime_price_comparison(self, symbols):
        """显示实时价格对比"""
        print(f"\n{'='*80}")
        print("⚡ 实时价格变化监控")
        print(f"{'='*80}")

        for symbol in symbols:
            try:
                # 获取最新ticker
                response = self.session.get(f"{self.base_url}/market/ticker?instId={symbol}", timeout=5)
                data = response.json()

                if data.get('code') == '0' and data.get('data'):
                    ticker = data['data'][0]
                    current_price = float(ticker.get('last', 0))
                    open_24h = float(ticker.get('open24h', 0))
                    high_24h = float(ticker.get('high24h', 0))
                    low_24h = float(ticker.get('low24h', 0))

                    change_24h = ((current_price - open_24h) / open_24h * 100) if open_24h > 0 else 0

                    # 计算价格位置
                    if high_24h > low_24h:
                        price_position = ((current_price - low_24h) / (high_24h - low_24h) * 100)
                    else:
                        price_position = 50

                    trend_icon = "🔥" if change_24h > 2 else "📈" if change_24h > 0 else "📉" if change_24h < -2 else "➡️"

                    print(f"📊 {symbol}:")
                    print(f"   💰 实时价格: {current_price:.6f} ({trend_icon}{change_24h:+.2f}%)")
                    print(f"   📈 24h区间: {low_24h:.6f} ~ {high_24h:.6f}")
                    print(f"   📍 价格位置: {price_position:.1f}% ({'高位' if price_position > 80 else '低位' if price_position < 20 else '中位'})")
                    print(f"   ⏰ 数据时间: {datetime.fromtimestamp(int(ticker.get('ts', 0))/1000).strftime('%H:%M:%S')}")

            except Exception as e:
                print(f"❌ 获取 {symbol} 实时数据失败: {e}")

            time.sleep(0.1)

def main():
    """主函数"""
    print("🚀 OKX交易所持续数据监控系统")
    print("=" * 80)
    print("🎯 持续监控功能:")
    print("  ✅ 持续获取所有永续合约数据")
    print("  ✅ 实时动态排行榜 (成交额/涨跌幅)")
    print("  ✅ 所有币种详细信息")
    print("  ✅ 持续价格走势分析 (K线数据)")
    print("  ✅ 自动数据保存 (CSV格式)")
    print("  ✅ 24小时不间断监控")
    print("  ✅ 技术指标计算")
    print("=" * 80)
    print("⚡ 更新频率: 每8秒")
    print("💾 数据保存: 每3个周期")
    print("📈 走势分析: 每个周期")
    print("🔄 自动重启: 出错时自动恢复")
    print("=" * 80)

    # 检查依赖
    try:
        import pandas as pd
        print("✅ 依赖检查通过")
    except ImportError:
        print("❌ 缺少pandas库，请运行: pip install pandas")
        return

    print("🚀 启动持续监控模式...")
    print("📊 将持续获取所有币种的走势和价格数据")
    print("⌨️  按 Ctrl+C 可以随时停止")

    monitor = OKXCompleteMonitor()

    # 添加持续监控方法
    def start_continuous_monitoring(self, update_interval=8):
        """开始持续监控所有币种"""
        print("🚀 启动OKX持续数据监控系统...")
        print("📡 连接到OKX官方API获取真实数据...")
        print("🔄 持续获取所有币种的走势和价格数据...")

        try:
            cycle_count = 0
            all_symbols_processed = set()

            while self.running:
                cycle_count += 1

                # 获取实时数据
                print(f"\n🔄 正在获取最新真实数据... (周期 #{cycle_count}) - {datetime.now().strftime('%H:%M:%S')}")
                tickers = self.get_all_tickers("SWAP")

                if tickers:
                    processed_data = self.process_ticker_data(tickers)

                    # 记录处理过的币种
                    for item in processed_data:
                        all_symbols_processed.add(item['symbol'])

                    # 验证数据真实性
                    if cycle_count == 1:
                        self.verify_data_authenticity(processed_data)

                    # 显示主仪表板
                    self.display_main_dashboard(processed_data)

                    # 显示热门币种详情
                    self.display_hot_coins_detail(processed_data, 10)

                    # 每个周期都获取热门币种的价格走势
                    hot_symbols = [item['symbol'] for item in
                                 sorted(processed_data, key=lambda x: x['volume_ccy_24h'], reverse=True)[:5]]
                    print(f"\n🎯 获取TOP5热门币种的实时走势数据...")
                    self.get_price_trends(hot_symbols, 7)

                    # 显示实时价格对比
                    self.show_realtime_price_comparison(hot_symbols)

                    # 获取更多币种的走势数据
                    if cycle_count % 2 == 0:
                        more_symbols = [item['symbol'] for item in
                                      sorted(processed_data, key=lambda x: x['volume_ccy_24h'], reverse=True)[5:10]]
                        print(f"\n📊 获取更多币种走势数据...")
                        self.get_price_trends(more_symbols, 5)

                    # 每3个周期保存一次数据
                    if cycle_count % 3 == 0:
                        self.save_data_to_files(processed_data)
                        print(f"💾 数据已保存 (周期 #{cycle_count})")

                    print(f"\n{'='*100}")
                    print(f"💡 OKX持续数据监控运行中... (周期 #{cycle_count})")
                    print(f"📊 当前监控: {len(processed_data)} 个永续合约")
                    print(f"📈 累计处理: {len(all_symbols_processed)} 个币种")
                    print(f"🔄 更新间隔: {update_interval} 秒")
                    print(f"📡 数据来源: OKX官方API (实时)")
                    print(f"✅ 功能: 持续获取所有币种走势和价格")
                    print("⌨️  按 Ctrl+C 退出监控")
                    print(f"{'='*100}")
                else:
                    print("❌ 获取数据失败，正在重试...")

                # 等待下次更新
                print(f"⏳ 等待 {update_interval} 秒后继续...")
                for i in range(update_interval):
                    if not self.running:
                        break
                    time.sleep(1)

        except KeyboardInterrupt:
            print("\n\n👋 监控已停止")
            print(f"📊 总共处理了 {len(all_symbols_processed)} 个币种")
            self.running = False
        except Exception as e:
            print(f"\n❌ 监控出错: {e}")
            print("🔄 5秒后自动重启...")
            time.sleep(5)
            if self.running:
                start_continuous_monitoring(self, update_interval)

    # 绑定方法到实例
    monitor.start_continuous_monitoring = start_continuous_monitoring.__get__(monitor, OKXCompleteMonitor)

    # 开始持续监控
    monitor.start_continuous_monitoring(update_interval=8)

if __name__ == "__main__":
    main()
