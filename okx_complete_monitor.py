#!/usr/bin/env python3
"""
OKX交易所完整数据监控工具
集成所有功能：实时数据、排行榜、价格走势、WebSocket监控
"""

import requests
import json
import time
import threading
from datetime import datetime, timedelta
import pandas as pd
import os
import sys

class OKXCompleteMonitor:
    def __init__(self):
        self.base_url = "https://www.okx.com/api/v5"
        self.ws_url = "wss://ws.okx.com:8443/ws/v5/public"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        self.running = True
        self.price_data = {}
        self.last_data = {}
        
    def clear_screen(self):
        """清屏"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def get_all_instruments(self, inst_type="SWAP"):
        """获取所有合约交易对信息"""
        url = f"{self.base_url}/public/instruments"
        params = {"instType": inst_type}
        
        try:
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            if data.get('code') == '0':
                return data.get('data', [])
            else:
                print(f"❌ 获取交易对失败: {data.get('msg')}")
                return []
        except Exception as e:
            print(f"❌ 请求交易对数据失败: {e}")
            return []
    
    def get_all_tickers(self, inst_type="SWAP"):
        """获取所有合约的ticker数据"""
        url = f"{self.base_url}/market/tickers"
        params = {"instType": inst_type}
        
        try:
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            if data.get('code') == '0':
                return data.get('data', [])
            else:
                print(f"❌ 获取ticker数据失败: {data.get('msg')}")
                return []
        except Exception as e:
            print(f"❌ 请求ticker数据失败: {e}")
            return []
    
    def get_candlestick_data(self, inst_id, bar="1D", limit=30):
        """获取K线数据"""
        url = f"{self.base_url}/market/candles"
        params = {
            "instId": inst_id,
            "bar": bar,
            "limit": str(limit)
        }
        
        try:
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            if data.get('code') == '0':
                return data.get('data', [])
            else:
                print(f"❌ 获取K线数据失败: {data.get('msg')}")
                return []
        except Exception as e:
            print(f"❌ 请求K线数据失败: {e}")
            return []
    
    def process_ticker_data(self, tickers):
        """处理ticker数据"""
        processed = []
        for ticker in tickers:
            try:
                inst_id = ticker.get('instId', '')
                last_price = float(ticker.get('last', 0))
                open_24h = float(ticker.get('open24h', 0))
                
                if open_24h > 0:
                    change_24h = ((last_price - open_24h) / open_24h * 100)
                else:
                    change_24h = 0
                
                # 计算价格变化趋势
                trend = "📈"
                if inst_id in self.last_data:
                    last_price_prev = self.last_data[inst_id]['price']
                    if last_price > last_price_prev:
                        trend = "🔥"
                    elif last_price < last_price_prev:
                        trend = "❄️"
                    else:
                        trend = "➡️"
                
                item = {
                    'symbol': inst_id,
                    'price': last_price,
                    'open24h': open_24h,
                    'high24h': float(ticker.get('high24h', 0)),
                    'low24h': float(ticker.get('low24h', 0)),
                    'change_24h': round(change_24h, 2),
                    'volume_24h': float(ticker.get('vol24h', 0)),
                    'volume_ccy_24h': float(ticker.get('volCcy24h', 0)),
                    'bid': float(ticker.get('bidPx', 0)),
                    'ask': float(ticker.get('askPx', 0)),
                    'trend': trend,
                    'timestamp': int(ticker.get('ts', 0))
                }
                processed.append(item)
                
                # 更新历史数据
                self.last_data[inst_id] = {'price': last_price}
                
            except Exception as e:
                continue
        
        return processed
    
    def display_main_dashboard(self, data):
        """显示主仪表板"""
        self.clear_screen()
        
        # 数据分类
        volume_ranking = sorted(data, key=lambda x: x['volume_ccy_24h'], reverse=True)[:25]
        gainers = sorted([x for x in data if x['change_24h'] > 0], 
                        key=lambda x: x['change_24h'], reverse=True)[:20]
        losers = sorted([x for x in data if x['change_24h'] < 0], 
                       key=lambda x: x['change_24h'])[:20]
        
        print("🚀 OKX交易所完整数据监控系统")
        print(f"⏰ 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📊 监控币种: {len(data)} 个永续合约")
        print(f"💰 总成交额: {sum(item['volume_ccy_24h'] for item in data):,.0f} USDT")
        
        # 成交额排行榜
        print(f"\n{'='*110}")
        print("💰 24小时成交额排行榜 (TOP 25)")
        print(f"{'='*110}")
        print(f"{'排名':<4} {'币种':<18} {'价格':<15} {'24h涨跌':<12} {'最高/最低':<20} {'成交额(USDT)':<18} {'趋势':<4}")
        print(f"{'-'*110}")
        
        for i, item in enumerate(volume_ranking, 1):
            change_color = "🔥" if item['change_24h'] > 5 else "📈" if item['change_24h'] > 0 else "📉" if item['change_24h'] < -5 else "➡️"
            high_low = f"{item['high24h']:.6f}/{item['low24h']:.6f}"
            print(f"{i:<4} {item['symbol']:<18} {item['price']:<15.6f} "
                  f"{change_color}{item['change_24h']:>7.2f}% {high_low:<20} {item['volume_ccy_24h']:>16,.0f} {item['trend']:<4}")
        
        # 涨幅榜
        print(f"\n{'='*90}")
        print("🚀 24小时涨幅榜 (TOP 20)")
        print(f"{'='*90}")
        print(f"{'排名':<4} {'币种':<18} {'价格':<15} {'涨幅':<10} {'成交额':<15} {'趋势':<4}")
        print(f"{'-'*90}")
        
        for i, item in enumerate(gainers, 1):
            print(f"{i:<4} {item['symbol']:<18} {item['price']:<15.6f} "
                  f"🔥{item['change_24h']:>6.2f}% {item['volume_ccy_24h']:>13,.0f} {item['trend']:<4}")
        
        # 跌幅榜
        print(f"\n{'='*90}")
        print("📉 24小时跌幅榜 (TOP 20)")
        print(f"{'='*90}")
        print(f"{'排名':<4} {'币种':<18} {'价格':<15} {'跌幅':<10} {'成交额':<15} {'趋势':<4}")
        print(f"{'-'*90}")
        
        for i, item in enumerate(losers, 1):
            print(f"{i:<4} {item['symbol']:<18} {item['price']:<15.6f} "
                  f"❄️{item['change_24h']:>6.2f}% {item['volume_ccy_24h']:>13,.0f} {item['trend']:<4}")
    
    def display_hot_coins_detail(self, data, count=8):
        """显示热门币种详细信息"""
        hot_coins = sorted(data, key=lambda x: x['volume_ccy_24h'], reverse=True)[:count]
        
        print(f"\n{'='*100}")
        print(f"🔥 热门币种实时详情 (TOP {count})")
        print(f"{'='*100}")
        
        for i, coin in enumerate(hot_coins, 1):
            spread = ((coin['ask'] - coin['bid']) / coin['price'] * 100) if coin['price'] > 0 else 0
            print(f"\n{i}. 📊 {coin['symbol']}")
            print(f"   💰 当前价格: {coin['price']:.6f} | 24h变化: {coin['change_24h']:+.2f}% {coin['trend']}")
            print(f"   📊 买一/卖一: {coin['bid']:.6f} / {coin['ask']:.6f} (价差: {spread:.3f}%)")
            print(f"   📈 24h区间: {coin['low24h']:.6f} ~ {coin['high24h']:.6f}")
            print(f"   💵 24h成交: {coin['volume_24h']:,.0f} 张 | {coin['volume_ccy_24h']:,.0f} USDT")
    
    def get_price_trends(self, symbols, days=7):
        """获取价格走势数据"""
        print(f"\n{'='*80}")
        print(f"📈 获取价格走势数据 (近{days}天)")
        print(f"{'='*80}")
        
        trends_data = {}
        for symbol in symbols:
            try:
                candles = self.get_candlestick_data(symbol, "1D", days)
                if candles:
                    trend_info = {
                        'symbol': symbol,
                        'data': []
                    }
                    
                    for candle in reversed(candles):  # 按时间正序
                        trend_info['data'].append({
                            'date': datetime.fromtimestamp(int(candle[0])/1000).strftime('%m-%d'),
                            'open': float(candle[1]),
                            'high': float(candle[2]),
                            'low': float(candle[3]),
                            'close': float(candle[4]),
                            'volume': float(candle[5])
                        })
                    
                    trends_data[symbol] = trend_info
                    
                    # 显示走势
                    print(f"\n📊 {symbol} 近{days}日走势:")
                    print(f"{'日期':<8} {'开盘':<12} {'最高':<12} {'最低':<12} {'收盘':<12} {'涨跌%':<8}")
                    print(f"{'-'*70}")
                    
                    for j, day_data in enumerate(trend_info['data']):
                        if j > 0:
                            prev_close = trend_info['data'][j-1]['close']
                            change = ((day_data['close'] - prev_close) / prev_close * 100)
                            change_str = f"{change:+.2f}%"
                        else:
                            change_str = "---"
                        
                        print(f"{day_data['date']:<8} {day_data['open']:<12.6f} {day_data['high']:<12.6f} "
                              f"{day_data['low']:<12.6f} {day_data['close']:<12.6f} {change_str:<8}")
                
                time.sleep(0.1)  # 避免请求过快
                
            except Exception as e:
                print(f"❌ 获取 {symbol} 走势失败: {e}")
        
        return trends_data
    
    def save_data_to_files(self, data):
        """保存数据到文件"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # 保存完整数据
            df_all = pd.DataFrame(data)
            df_all.to_csv(f"okx_all_data_{timestamp}.csv", index=False, encoding='utf-8-sig')
            
            # 保存成交额排行
            volume_ranking = sorted(data, key=lambda x: x['volume_ccy_24h'], reverse=True)[:50]
            df_volume = pd.DataFrame(volume_ranking)
            df_volume.to_csv(f"okx_volume_ranking_{timestamp}.csv", index=False, encoding='utf-8-sig')
            
            # 保存涨跌幅排行
            gainers = sorted([x for x in data if x['change_24h'] > 0], 
                           key=lambda x: x['change_24h'], reverse=True)[:30]
            losers = sorted([x for x in data if x['change_24h'] < 0], 
                          key=lambda x: x['change_24h'])[:30]
            
            df_gainers = pd.DataFrame(gainers)
            df_losers = pd.DataFrame(losers)
            df_gainers.to_csv(f"okx_gainers_{timestamp}.csv", index=False, encoding='utf-8-sig')
            df_losers.to_csv(f"okx_losers_{timestamp}.csv", index=False, encoding='utf-8-sig')
            
            print(f"\n✅ 数据已保存:")
            print(f"   📄 完整数据: okx_all_data_{timestamp}.csv")
            print(f"   📄 成交额排行: okx_volume_ranking_{timestamp}.csv")
            print(f"   📄 涨幅榜: okx_gainers_{timestamp}.csv")
            print(f"   📄 跌幅榜: okx_losers_{timestamp}.csv")
            
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
    
    def start_complete_monitoring(self, update_interval=15):
        """开始完整监控"""
        print("🚀 启动OKX完整数据监控系统...")
        
        try:
            cycle_count = 0
            while self.running:
                cycle_count += 1
                
                # 获取实时数据
                print("🔄 正在获取最新数据...")
                tickers = self.get_all_tickers("SWAP")
                
                if tickers:
                    processed_data = self.process_ticker_data(tickers)
                    
                    # 显示主仪表板
                    self.display_main_dashboard(processed_data)
                    
                    # 显示热门币种详情
                    self.display_hot_coins_detail(processed_data, 8)
                    
                    # 每3个周期获取一次价格走势
                    if cycle_count % 3 == 1:
                        hot_symbols = [item['symbol'] for item in 
                                     sorted(processed_data, key=lambda x: x['volume_ccy_24h'], reverse=True)[:5]]
                        self.get_price_trends(hot_symbols, 7)
                    
                    # 每5个周期保存一次数据
                    if cycle_count % 5 == 0:
                        self.save_data_to_files(processed_data)
                    
                    print(f"\n{'='*80}")
                    print(f"💡 完整监控运行中... (周期 #{cycle_count})")
                    print(f"🔄 下次更新: {update_interval} 秒后")
                    print("📊 功能: 实时数据 + 排行榜 + 价格走势 + 数据保存")
                    print("⌨️  按 Ctrl+C 退出监控")
                    print(f"{'='*80}")
                
                # 等待下次更新
                for i in range(update_interval):
                    if not self.running:
                        break
                    time.sleep(1)
                    
        except KeyboardInterrupt:
            print("\n\n👋 监控已停止")
            self.running = False
        except Exception as e:
            print(f"\n❌ 监控出错: {e}")

def main():
    """主函数"""
    print("🚀 OKX交易所完整数据监控系统")
    print("=" * 80)
    print("🎯 集成功能:")
    print("  ✅ 实时获取所有永续合约数据")
    print("  ✅ 动态排行榜 (成交额/涨跌幅)")
    print("  ✅ 热门币种详细信息")
    print("  ✅ 价格走势分析 (K线数据)")
    print("  ✅ 数据自动保存 (CSV格式)")
    print("  ✅ 实时趋势监控")
    print("=" * 80)
    print("⚡ 更新频率: 每15秒")
    print("💾 数据保存: 每5个周期")
    print("📈 走势分析: 每3个周期")
    print("=" * 80)
    
    # 检查依赖
    try:
        import pandas as pd
        print("✅ 依赖检查通过")
    except ImportError:
        print("❌ 缺少pandas库，请运行: pip install pandas")
        return
    
    input("按回车键开始完整监控...")
    
    monitor = OKXCompleteMonitor()
    monitor.start_complete_monitoring(update_interval=15)

if __name__ == "__main__":
    main()
