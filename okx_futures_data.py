#!/usr/bin/env python3
"""
OKX交易所合约币种排行榜和价格走势获取工具
获取所有合约币种的实时价格、24小时涨跌幅、成交量等数据
"""

import requests
import json
import pandas as pd
import time
from datetime import datetime, timedelta
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OKXFuturesData:
    def __init__(self):
        self.base_url = "https://www.okx.com"
        self.api_base = "/api/v5"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        })
    
    def get_all_instruments(self, inst_type="SWAP"):
        """
        获取所有合约交易对信息
        inst_type: SPOT(现货), MARGIN(杠杆), SWAP(永续合约), FUTURES(交割合约), OPTION(期权)
        """
        url = f"{self.base_url}{self.api_base}/public/instruments"
        params = {"instType": inst_type}
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            if data.get('code') == '0':
                return data.get('data', [])
            else:
                logger.error(f"获取交易对失败: {data.get('msg')}")
                return []
        except Exception as e:
            logger.error(f"请求交易对数据失败: {e}")
            return []
    
    def get_all_tickers(self, inst_type="SWAP"):
        """
        获取所有合约的ticker数据（价格、涨跌幅、成交量等）
        """
        url = f"{self.base_url}{self.api_base}/market/tickers"
        params = {"instType": inst_type}
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            if data.get('code') == '0':
                return data.get('data', [])
            else:
                logger.error(f"获取ticker数据失败: {data.get('msg')}")
                return []
        except Exception as e:
            logger.error(f"请求ticker数据失败: {e}")
            return []
    
    def get_candlestick_data(self, inst_id, bar="1D", limit=100):
        """
        获取K线数据（价格走势）
        bar: 1m, 3m, 5m, 15m, 30m, 1H, 2H, 4H, 6H, 12H, 1D, 1W, 1M, 3M, 6M, 1Y
        """
        url = f"{self.base_url}{self.api_base}/market/candles"
        params = {
            "instId": inst_id,
            "bar": bar,
            "limit": str(limit)
        }
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            if data.get('code') == '0':
                return data.get('data', [])
            else:
                logger.error(f"获取K线数据失败: {data.get('msg')}")
                return []
        except Exception as e:
            logger.error(f"请求K线数据失败: {e}")
            return []
    
    def format_ticker_data(self, tickers):
        """
        格式化ticker数据为易读格式
        """
        formatted_data = []
        
        for ticker in tickers:
            try:
                # 计算24小时涨跌幅百分比
                last_price = float(ticker.get('last', 0))
                open_24h = float(ticker.get('open24h', 0))
                change_24h = ((last_price - open_24h) / open_24h * 100) if open_24h > 0 else 0
                
                formatted_item = {
                    '交易对': ticker.get('instId', ''),
                    '最新价格': last_price,
                    '24h开盘价': open_24h,
                    '24h最高价': float(ticker.get('high24h', 0)),
                    '24h最低价': float(ticker.get('low24h', 0)),
                    '24h涨跌幅(%)': round(change_24h, 2),
                    '24h成交量': float(ticker.get('vol24h', 0)),
                    '24h成交额': float(ticker.get('volCcy24h', 0)),
                    '买一价': float(ticker.get('bidPx', 0)),
                    '卖一价': float(ticker.get('askPx', 0)),
                    '更新时间': datetime.fromtimestamp(int(ticker.get('ts', 0))/1000).strftime('%Y-%m-%d %H:%M:%S')
                }
                formatted_data.append(formatted_item)
            except Exception as e:
                logger.warning(f"格式化数据失败: {e}")
                continue
        
        return formatted_data
    
    def get_top_contracts_by_volume(self, limit=50):
        """
        获取按成交量排序的顶级合约
        """
        logger.info("正在获取永续合约数据...")
        tickers = self.get_all_tickers("SWAP")
        
        if not tickers:
            logger.error("未能获取到ticker数据")
            return []
        
        formatted_data = self.format_ticker_data(tickers)
        
        # 按24小时成交额排序
        sorted_data = sorted(formatted_data, key=lambda x: x['24h成交额'], reverse=True)
        
        return sorted_data[:limit]
    
    def get_top_gainers_losers(self, limit=20):
        """
        获取涨跌幅排行榜
        """
        logger.info("正在获取涨跌幅排行榜...")
        tickers = self.get_all_tickers("SWAP")
        
        if not tickers:
            return [], []
        
        formatted_data = self.format_ticker_data(tickers)
        
        # 过滤掉涨跌幅为0的数据
        valid_data = [item for item in formatted_data if item['24h涨跌幅(%)'] != 0]
        
        # 涨幅榜
        gainers = sorted(valid_data, key=lambda x: x['24h涨跌幅(%)'], reverse=True)[:limit]
        
        # 跌幅榜
        losers = sorted(valid_data, key=lambda x: x['24h涨跌幅(%)'])[:limit]
        
        return gainers, losers
    
    def save_to_csv(self, data, filename):
        """
        保存数据到CSV文件
        """
        try:
            df = pd.DataFrame(data)
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            logger.info(f"数据已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存CSV文件失败: {e}")
    
    def print_ranking_table(self, data, title, limit=10):
        """
        打印排行榜表格
        """
        print(f"\n{'='*80}")
        print(f"{title}")
        print(f"{'='*80}")
        print(f"{'排名':<4} {'交易对':<15} {'最新价格':<12} {'24h涨跌幅':<10} {'24h成交额':<15}")
        print(f"{'-'*80}")
        
        for i, item in enumerate(data[:limit], 1):
            change_color = "📈" if item['24h涨跌幅(%)'] > 0 else "📉" if item['24h涨跌幅(%)'] < 0 else "➡️"
            print(f"{i:<4} {item['交易对']:<15} {item['最新价格']:<12.4f} "
                  f"{change_color}{item['24h涨跌幅(%)']:>6.2f}% {item['24h成交额']:<15,.0f}")

def main():
    """
    主函数
    """
    okx = OKXFuturesData()
    
    print("🚀 OKX交易所合约币种数据获取工具")
    print("正在获取数据，请稍候...")
    
    # 获取成交量排行榜
    top_volume = okx.get_top_contracts_by_volume(50)
    if top_volume:
        okx.print_ranking_table(top_volume, "📊 永续合约成交额排行榜 (TOP 20)", 20)
        okx.save_to_csv(top_volume, f"okx_top_volume_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
    
    # 获取涨跌幅排行榜
    gainers, losers = okx.get_top_gainers_losers(20)
    
    if gainers:
        okx.print_ranking_table(gainers, "🔥 24小时涨幅榜 (TOP 10)", 10)
        okx.save_to_csv(gainers, f"okx_gainers_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
    
    if losers:
        okx.print_ranking_table(losers, "❄️ 24小时跌幅榜 (TOP 10)", 10)
        okx.save_to_csv(losers, f"okx_losers_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
    
    # 获取热门币种的价格走势
    print(f"\n{'='*80}")
    print("📈 获取热门币种价格走势数据...")
    print(f"{'='*80}")
    
    if top_volume:
        # 获取前5个热门币种的K线数据
        for i, coin in enumerate(top_volume[:5], 1):
            inst_id = coin['交易对']
            print(f"\n正在获取 {inst_id} 的价格走势数据...")
            
            # 获取日K线数据
            candles = okx.get_candlestick_data(inst_id, "1D", 30)
            if candles:
                print(f"✅ {inst_id} - 获取到 {len(candles)} 天的K线数据")
                
                # 保存K线数据
                candle_data = []
                for candle in candles:
                    candle_data.append({
                        '时间': datetime.fromtimestamp(int(candle[0])/1000).strftime('%Y-%m-%d'),
                        '开盘价': float(candle[1]),
                        '最高价': float(candle[2]),
                        '最低价': float(candle[3]),
                        '收盘价': float(candle[4]),
                        '成交量': float(candle[5])
                    })
                
                filename = f"okx_{inst_id.replace('-', '_')}_candles_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                okx.save_to_csv(candle_data, filename)
            else:
                print(f"❌ {inst_id} - 获取K线数据失败")
            
            # 避免请求过于频繁
            time.sleep(0.1)
    
    print(f"\n{'='*80}")
    print("✅ 数据获取完成！所有数据已保存为CSV文件。")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
